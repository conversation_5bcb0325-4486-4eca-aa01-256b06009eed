import 'package:flutter/material.dart';
import '../models/chat_message.dart';

class ChatProvider extends ChangeNotifier {
  final List<ChatSession> _sessions = [];
  final List<FAQ> _faqs = [];
  ChatSession? _currentSession;
  bool _isTyping = false;
  bool _isOnline = true;

  List<ChatSession> get sessions => List.unmodifiable(_sessions);
  List<FAQ> get faqs => List.unmodifiable(_faqs);
  ChatSession? get currentSession => _currentSession;
  bool get isTyping => _isTyping;
  bool get isOnline => _isOnline;

  int get totalUnreadMessages {
    return _sessions.fold(0, (sum, session) => sum + session.unreadCount);
  }

  // Chat session döretmek
  ChatSession createNewSession({String? title}) {
    final session = ChatSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? 'Täze söhbetdeşlik',
      createdAt: DateTime.now(),
      lastMessageAt: DateTime.now(),
      messages: [],
      agentName: 'Baglan Market Goldawy',
      agentAvatar: 'assets/images/baglan.png',
    );

    _sessions.insert(0, session);
    _currentSession = session;
    
    // Hoş geldiňiz habary
    _addSystemMessage(session.id, 'Baglan Market-a hoş geldiňiz! Size nähili kömek edip bilerin?');
    
    notifyListeners();
    return session;
  }

  // Session saýlamak
  void selectSession(String sessionId) {
    _currentSession = _sessions.firstWhere(
      (session) => session.id == sessionId,
      orElse: () => createNewSession(),
    );
    
    // Okalmaýan habarlary okaldy diýip bellemek
    _markMessagesAsRead(sessionId);
    notifyListeners();
  }

  // Habar ibermek
  void sendMessage(String content, {MessageType type = MessageType.text}) {
    _currentSession ??= createNewSession();

    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isFromUser: true,
      timestamp: DateTime.now(),
      type: type,
      status: MessageStatus.sending,
    );

    _addMessageToSession(_currentSession!.id, message);
    
    // Habar iberildi diýip bellemek
    Future.delayed(const Duration(milliseconds: 500), () {
      _updateMessageStatus(message.id, MessageStatus.sent);
    });

    // Awtomatik jogap
    _simulateAgentResponse(content);
  }

  // Habar goşmak
  void _addMessageToSession(String sessionId, ChatMessage message) {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = _sessions[sessionIndex];
      final updatedMessages = List<ChatMessage>.from(session.messages)..add(message);
      
      _sessions[sessionIndex] = session.copyWith(
        messages: updatedMessages,
        lastMessageAt: message.timestamp,
      );
      
      notifyListeners();
    }
  }

  // Ulgam habaryny goşmak
  void _addSystemMessage(String sessionId, String content) {
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isFromUser: false,
      timestamp: DateTime.now(),
      type: MessageType.system,
      status: MessageStatus.delivered,
    );

    _addMessageToSession(sessionId, message);
  }

  // Habar statusyny täzelemek
  void _updateMessageStatus(String messageId, MessageStatus status) {
    for (int i = 0; i < _sessions.length; i++) {
      final session = _sessions[i];
      final messageIndex = session.messages.indexWhere((m) => m.id == messageId);
      
      if (messageIndex != -1) {
        final updatedMessages = List<ChatMessage>.from(session.messages);
        updatedMessages[messageIndex] = updatedMessages[messageIndex].copyWith(status: status);
        
        _sessions[i] = session.copyWith(messages: updatedMessages);
        notifyListeners();
        break;
      }
    }
  }

  // Habarlary okaldy diýip bellemek
  void _markMessagesAsRead(String sessionId) {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      final session = _sessions[sessionIndex];
      final updatedMessages = session.messages.map((msg) {
        if (!msg.isFromUser && msg.status != MessageStatus.read) {
          return msg.copyWith(status: MessageStatus.read);
        }
        return msg;
      }).toList();
      
      _sessions[sessionIndex] = session.copyWith(messages: updatedMessages);
      notifyListeners();
    }
  }

  // Agent jogabyny simulasiýa etmek
  void _simulateAgentResponse(String userMessage) {
    if (_currentSession == null) return;

    _isTyping = true;
    notifyListeners();

    Future.delayed(const Duration(seconds: 2), () {
      _isTyping = false;
      
      String response = _generateResponse(userMessage.toLowerCase());
      
      final message = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: response,
        isFromUser: false,
        timestamp: DateTime.now(),
        type: MessageType.text,
        status: MessageStatus.delivered,
      );

      _addMessageToSession(_currentSession!.id, message);
    });
  }

  // Jogap döretmek
  String _generateResponse(String userMessage) {
    // Ýönekeý AI jogap algoritmi
    if (userMessage.contains('salam') || userMessage.contains('hoş')) {
      return 'Salam! Baglan Market-a hoş geldiňiz. Size nähili kömek edip bilerin?';
    } else if (userMessage.contains('haryt') || userMessage.contains('önüm')) {
      return 'Harytlarymyz barada soraglaryňyz barmy? Kategoriýalar, bahalar ýa-da beýleki maglumatlar üçin sorap bilersiňiz.';
    } else if (userMessage.contains('baha') || userMessage.contains('nyrh')) {
      return 'Harytlarymyzyň bahalary bäsdeşlik derejesinde. Arzanladyşlar we ýörite tekliplerden peýdalanyp bilersiňiz.';
    } else if (userMessage.contains('eltip bermek') || userMessage.contains('dostawka')) {
      return 'Eltip bermek hyzmaty Aşgabat şäheriniň içinde 2-3 sagadyň içinde, beýleki şäherler üçin 1-2 günüň içinde amala aşyrylýar.';
    } else if (userMessage.contains('töleg') || userMessage.contains('karta')) {
      return 'Töleg üçin nagt pul, bank kartalary, Rysgal, TurkmenCard we beýleki ýerli töleg ulgamlaryny kabul edýäris.';
    } else if (userMessage.contains('gaýtarmak') || userMessage.contains('çalyşmak')) {
      return 'Harytlary 14 günüň içinde gaýtaryp ýa-da çalyşyp bilersiňiz. Jikme-jik maglumat üçin gaýtarmak syýasatymyzy okaň.';
    } else if (userMessage.contains('goldaw') || userMessage.contains('kömek')) {
      return 'Goldaw hyzmaty 24/7 elýeterli. Telefon: +993 12 123456, Email: <EMAIL>';
    } else if (userMessage.contains('sag bol') || userMessage.contains('hoş gal')) {
      return 'Sag boluň! Başga soraglaryňyz bolsa, hemişe kömek bermäge taýyn. Baglan Market-y saýlanyňyz üçin sag boluň!';
    } else {
      return 'Soraglaryňyzy düşünmedim. Harytlar, bahalar, eltip bermek, töleg usullary ýa-da beýleki meseleler barada sorap bilersiňiz. Ýa-da telefon arkaly habarlaşyp bilersiňiz: +993 12 123456';
    }
  }

  // Session pozup ýok etmek
  void deleteSession(String sessionId) {
    _sessions.removeWhere((session) => session.id == sessionId);
    if (_currentSession?.id == sessionId) {
      _currentSession = _sessions.isNotEmpty ? _sessions.first : null;
    }
    notifyListeners();
  }

  // Session arhiwlemek
  void archiveSession(String sessionId) {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
        status: SessionStatus.archived,
      );
      notifyListeners();
    }
  }

  // FAQ goşmak
  void addFAQ(FAQ faq) {
    _faqs.add(faq);
    notifyListeners();
  }

  // FAQ gözlemek
  List<FAQ> searchFAQs(String query) {
    if (query.isEmpty) return _faqs;
    
    return _faqs.where((faq) {
      return faq.question.toLowerCase().contains(query.toLowerCase()) ||
             faq.answer.toLowerCase().contains(query.toLowerCase()) ||
             faq.keywords.any((keyword) => keyword.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  // Demo maglumatlar goşmak
  void addDemoData() {
    // Demo FAQ-lar
    final demoFAQs = [
      FAQ(
        id: '1',
        question: 'Eltip bermek näçe wagt alýar?',
        answer: 'Aşgabat şäheriniň içinde 2-3 sagat, beýleki şäherler üçin 1-2 gün.',
        category: 'Eltip bermek',
        keywords: ['eltip bermek', 'dostawka', 'wagt', 'sagat'],
      ),
      FAQ(
        id: '2',
        question: 'Haýsy töleg usullaryny kabul edýärsiňiz?',
        answer: 'Nagt pul, bank kartalary, Rysgal, TurkmenCard we beýleki ýerli töleg ulgamlary.',
        category: 'Töleg',
        keywords: ['töleg', 'karta', 'nagt', 'rysgal'],
      ),
      FAQ(
        id: '3',
        question: 'Harytlary gaýtaryp bilýärinmi?',
        answer: 'Hawa, 14 günüň içinde harytlary gaýtaryp ýa-da çalyşyp bilersiňiz.',
        category: 'Gaýtarmak',
        keywords: ['gaýtarmak', 'çalyşmak', '14 gün'],
      ),
      FAQ(
        id: '4',
        question: 'Goldaw hyzmaty näçe sagat işleýär?',
        answer: 'Goldaw hyzmaty 24/7 elýeterli. Telefon: +993 12 123456',
        category: 'Goldaw',
        keywords: ['goldaw', 'kömek', '24/7', 'telefon'],
      ),
    ];

    _faqs.addAll(demoFAQs);
    notifyListeners();
  }

  // Online/offline statusy üýtgetmek
  void toggleOnlineStatus() {
    _isOnline = !_isOnline;
    notifyListeners();
  }

  // Ähli sessionsları arassalamak
  void clearAllSessions() {
    _sessions.clear();
    _currentSession = null;
    notifyListeners();
  }

  // Surat ibermek
  void sendImage(String imagePath) {
    _currentSession ??= createNewSession();

    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: 'Surat iberildi',
      isFromUser: true,
      timestamp: DateTime.now(),
      type: MessageType.image,
      status: MessageStatus.sending,
      imageUrl: imagePath,
    );

    _addMessageToSession(_currentSession!.id, message);

    // Habar iberildi diýip bellemek
    Future.delayed(const Duration(milliseconds: 500), () {
      _updateMessageStatus(message.id, MessageStatus.sent);
    });

    // Agent jogaby
    Future.delayed(const Duration(seconds: 1), () {
      final response = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: 'Suraty gördüm. Bu haryt barada has giňişleýin maglumat berip bilerin.',
        isFromUser: false,
        timestamp: DateTime.now(),
        type: MessageType.text,
        status: MessageStatus.delivered,
      );
      _addMessageToSession(_currentSession!.id, response);
    });
  }

  // Tiz jogap düwmelerini almak
  List<QuickReply> getQuickReplies() {
    return [
      QuickReply(id: '1', text: 'Harytlar barada', payload: 'harytlar'),
      QuickReply(id: '2', text: 'Eltip bermek', payload: 'eltip_bermek'),
      QuickReply(id: '3', text: 'Töleg usullary', payload: 'toleg'),
      QuickReply(id: '4', text: 'Goldaw hyzmaty', payload: 'goldaw'),
      QuickReply(id: '5', text: 'Gaýtarmak syýasaty', payload: 'gaytarmak'),
    ];
  }

  // Tiz jogap işlemek
  void handleQuickReply(QuickReply reply) {
    sendMessage(reply.text);
  }
}
